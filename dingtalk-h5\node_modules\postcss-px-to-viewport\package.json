{"name": "postcss-px-to-viewport", "description": "A CSS post-processor that converts px to viewport units (vw, vh, vmin, vmax).", "version": "1.1.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "**************:evrone/postcss-px-to-viewport.git"}, "bugs": "https://github.com/evrone/postcss-px-to-viewport/issues", "homepage": "https://github.com/evrone/postcss-px-to-viewport", "main": "index.js", "scripts": {"test": "jasmine-node spec"}, "devDependencies": {"jasmine-node": "~1.11.0"}, "keywords": ["css", "units", "pixel", "px", "viewport", "vw", "vh", "vmin", "vmax", "postcss", "postcss-plugin"], "dependencies": {"object-assign": ">=4.0.1", "postcss": ">=5.0.2"}}