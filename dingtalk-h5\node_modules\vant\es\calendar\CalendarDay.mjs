import { computed, defineComponent, createVNode as _createVNode } from "vue";
import { makeNumberProp, createNamespace, makeRequiredProp } from "../utils/index.mjs";
import { bem, isLastRowInMonth } from "./utils.mjs";
const [name] = createNamespace("calendar-day");
var stdin_default = defineComponent({
  name,
  props: {
    item: makeRequiredProp(Object),
    color: String,
    index: Number,
    offset: makeNumberProp(0),
    rowHeight: String
  },
  emits: ["click", "clickDisabledDate"],
  setup(props, {
    emit,
    slots
  }) {
    const style = computed(() => {
      const {
        item,
        index,
        color,
        offset,
        rowHeight
      } = props;
      const style2 = {
        height: rowHeight
      };
      if (item.type === "placeholder") {
        style2.width = "100%";
        return style2;
      }
      if (index === 0) {
        style2.marginLeft = `${100 * offset / 7}%`;
      }
      if (color) {
        switch (item.type) {
          case "end":
          case "start":
          case "start-end":
          case "multiple-middle":
          case "multiple-selected":
            style2.background = color;
            break;
          case "middle":
            style2.color = color;
            break;
        }
      }
      if (item.date && isLastRowInMonth(item.date, offset)) {
        style2.marginBottom = 0;
      }
      return style2;
    });
    const onClick = () => {
      if (props.item.type !== "disabled") {
        emit("click", props.item);
      } else {
        emit("clickDisabledDate", props.item);
      }
    };
    const renderTopInfo = () => {
      const {
        topInfo
      } = props.item;
      if (topInfo || slots["top-info"]) {
        return _createVNode("div", {
          "class": bem("top-info")
        }, [slots["top-info"] ? slots["top-info"](props.item) : topInfo]);
      }
    };
    const renderBottomInfo = () => {
      const {
        bottomInfo
      } = props.item;
      if (bottomInfo || slots["bottom-info"]) {
        return _createVNode("div", {
          "class": bem("bottom-info")
        }, [slots["bottom-info"] ? slots["bottom-info"](props.item) : bottomInfo]);
      }
    };
    const renderText = () => {
      return slots.text ? slots.text(props.item) : props.item.text;
    };
    const renderContent = () => {
      const {
        item,
        color,
        rowHeight
      } = props;
      const {
        type
      } = item;
      const Nodes = [renderTopInfo(), renderText(), renderBottomInfo()];
      if (type === "selected") {
        return _createVNode("div", {
          "class": bem("selected-day"),
          "style": {
            width: rowHeight,
            height: rowHeight,
            background: color
          }
        }, [Nodes]);
      }
      return Nodes;
    };
    return () => {
      const {
        type,
        className
      } = props.item;
      if (type === "placeholder") {
        return _createVNode("div", {
          "class": bem("day"),
          "style": style.value
        }, null);
      }
      return _createVNode("div", {
        "role": "gridcell",
        "style": style.value,
        "class": [bem("day", type), className],
        "tabindex": type === "disabled" ? void 0 : -1,
        "onClick": onClick
      }, [renderContent()]);
    };
  }
});
export {
  stdin_default as default
};
