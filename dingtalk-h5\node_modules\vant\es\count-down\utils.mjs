import { pad<PERSON>ero } from "../utils/index.mjs";
function parseFormat(format, currentTime) {
  const { days } = currentTime;
  let { hours, minutes, seconds, milliseconds } = currentTime;
  if (format.includes("DD")) {
    format = format.replace("DD", pad<PERSON>ero(days));
  } else {
    hours += days * 24;
  }
  if (format.includes("HH")) {
    format = format.replace("HH", pad<PERSON>ero(hours));
  } else {
    minutes += hours * 60;
  }
  if (format.includes("mm")) {
    format = format.replace("mm", padZero(minutes));
  } else {
    seconds += minutes * 60;
  }
  if (format.includes("ss")) {
    format = format.replace("ss", padZero(seconds));
  } else {
    milliseconds += seconds * 1e3;
  }
  if (format.includes("S")) {
    const ms = padZero(milliseconds, 3);
    if (format.includes("SSS")) {
      format = format.replace("SSS", ms);
    } else if (format.includes("SS")) {
      format = format.replace("SS", ms.slice(0, 2));
    } else {
      format = format.replace("S", ms.charAt(0));
    }
  }
  return format;
}
export {
  parseFormat
};
