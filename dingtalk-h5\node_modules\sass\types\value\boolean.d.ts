import {Value} from './index';

/**
 * Sass's [`true` value](https://sass-lang.com/documentation/values/booleans).
 *
 * @category Custom Function
 */
export const sassTrue: SassBoolean;

/**
 * Sass's [`false` value](https://sass-lang.com/documentation/values/booleans).
 *
 * @category Custom Function
 */
export const sassFalse: SassBoolean;

/**
 * Sass's [boolean type](https://sass-lang.com/documentation/values/booleans).
 *
 * @category Custom Function
 */
export class SassBoolean extends Value {
  private constructor();

  /**
   * Whether this value is `true` or `false`.
   */
  get value(): boolean;
}
