# Contributing Guidelines

Thanks for taking the time to contribute!

The following is a set of guidelines for contributing to this project. These are just guidelines, not rules, so use your best judgement and feel free to propose changes to this document in a pull request.

## Reporting issues

Ensure the bug was not already reported by searching on GitHub under issues. If you're unable to find an open issue addressing the bug, open a new issue.

Please pay attention to the following points while opening an issue:
* How to reproduce the issue, step-by-step.
* The expected behavior (or what is wrong).
* Screenshots for GUI issues.
* The application version.
* The operating system.

## Pull Requests

Pull Requests are always welcome.

1. When you edit the code, please check the formatting of your code before you `git commit`.
2. Ensure the PR description clearly describes the problem and solution. It should include:
	* The operating system on which you tested.
	* The relevant issue number, if applicable.
